<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="description" content="">
    <meta name="author" content="">
    <meta name="_csrf" th:content="${_csrf.token}" />
    <meta name="_csrf_header" th:content="${_csrf.headerName}" />

    <link rel="icon" th:href="@{/images/san_miguel_logo.ico}">

    <title>Admin - Servicios</title>

    <!-- Vendors Style-->

    <!-- Style-->
    <link rel="stylesheet" th:href="@{/css/vendors_css.css}">
    <link rel="stylesheet" th:href="@{/css/style.css}">
    <link rel="stylesheet" th:href="@{/css/skin_color.css}">

    <!-- DataTables CSS -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/@mdi/font/css/materialdesignicons.min.css">


    <style>

        .dataTables_filter {
            margin-bottom: 20px; /* Separación */
            float: right; /* Puedes quitar esto si lo quieres alineado a la izquierda */
        }
    </style>

</head>

<body class="hold-transition light-skin sidebar-mini theme-success fixed">

<div class="wrapper">
    <div id="loader"></div>

    <header class="main-header">

        <div class="d-flex align-items-center logo-box justify-content-center">
            <!-- Logo -->
            <a th:href="@{/admin}" class="logo">
                <!-- logo-->
                <div class="logo-mini w-150 text-center">
                    <span class="light-logo"><img th:src="@{/images/logo-sanMiguel.png}" alt="logo"></span>
                </div>
            </a>
        </div>
        <!-- Header Navbar -->
        <nav class="navbar navbar-static-top">
            <!-- Sidebar toggle button-->
            <div class="app-menu">
                <ul class="header-megamenu nav">
                    <li class="btn-group nav-item">
                        <a href="#" class="waves-effect waves-light nav-link push-btn btn-primary-light" data-toggle="push-menu" role="button">
                            <i data-feather="align-left"></i>
                        </a>
                    </li>

                </ul>
            </div>

            <div th:replace="fragments :: navbarAdmin"></div>
        </nav>
    </header>

    <aside th:replace="fragments :: sideBarAdmin"></aside>

    <!-- Content Wrapper. Contains page content -->
    <div class="content-wrapper">
        <div class="container-full">
            <!-- Content Header (Page header) -->
            <div class="content-header">
                <div class="d-flex justify-content-between align-items-center mb-2">
                    <h2 class="fw-bold mb-0 me-3">Servicios</h2>
                    <div>
                        <a th:href="@{/admin/nuevo}" class="btn btn-sm text-white" style="background-color: #008B9A;">
                            <i data-feather="plus"></i> Agregar Servicio
                        </a>
                    </div>
                </div>
            </div>

            <!-- Main content -->
            <section class="content">
                <div class="row mt-2">
                    <div class="col-12">
                        <div class="box">

                            <div class="box-body">
                                <div class="table-responsive">
                                    <table class="table table-bordered table-striped" id="tablaServicios">
                                        <thead>
                                        <tr>
                                            <th>Tipo de Servicio</th>
                                            <th>Nombre</th>
                                            <th>Ubicación</th>
                                            <th>Aforo</th>
                                            <th>Horario</th>
                                            <th>Opciones</th>
                                        </tr>
                                        </thead>
                                        <tbody>
                                        <!-- Iteración sobre la lista de espacios deportivos -->
                                        <tr th:each="servicio : ${listaEspacios}" th:attr="data-id=${servicio.id}">
                                            <td th:text="${servicio.tipoEspacio.nombre}"></td>
                                            <td th:text="${servicio.nombre}"></td>
                                            <td th:text="${servicio.ubicacion}"></td>
                                            <td th:text="${servicio.aforo != null ? servicio.aforo : '-'}"></td>
                                            <td th:text="${servicio.horaAbre + ' - ' + servicio.horaCierra}"></td>
                                            <td>
                                                <button type="button"
                                                        class="btn btn-sm btn-warning me-1"
                                                        title="Editar"
                                                        data-bs-toggle="modal"
                                                        data-bs-target="#modalEditar"
                                                        onclick="cargarDatosModal(this)">
                                                    <i class="mdi mdi-pencil"></i>
                                                </button>

                                                <button type="button"
                                                        class="btn btn-sm btn-info me-1"
                                                        title="Mantenimiento"
                                                        onclick="abrirMantenimiento(this)">
                                                    <i class="mdi mdi-wrench"></i>
                                                </button>

                                                <button type="button"
                                                        class="btn btn-sm btn-danger delete-btn"
                                                        title="Eliminar"
                                                        th:attr="data-id=${servicio.id}"
                                                        data-bs-toggle="modal"
                                                        data-bs-target="#modalEliminar">
                                                    <i class="mdi mdi-delete"></i>
                                                </button>
                                                <div class="btn-group dropdown">
                                                    <button type="button" class="btn btn-sm btn-info dropdown-toggle" data-bs-toggle="dropdown" aria-expanded="false">
                                                        <i class="mdi mdi-download"></i> Reporte
                                                    </button>
                                                    <ul class="dropdown-menu">
                                                        <li>
                                                            <a class="dropdown-item"
                                                               th:href="@{/admin/servicios/exportar-reporte-pdf(id=${servicio.id})}">
                                                                <i class="mdi mdi-file-pdf text-danger"></i> Descargar PDF
                                                            </a>
                                                        </li>
                                                        <li>
                                                            <a class="dropdown-item"
                                                               th:href="@{/admin/servicios/exportar-reporte-excel(id=${servicio.id})}">
                                                                <i class="mdi mdi-file-excel text-success"></i> Descargar Excel
                                                            </a>
                                                        </li>
                                                    </ul>
                                                </div>


                                            </td>
                                        </tr>
                                        </tbody>

                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </section>
            <!-- /.content -->
        </div>
    </div>
    <!-- /.content-wrapper -->
    <footer class="main-footer">
        <div class="pull-right d-none d-sm-inline-block">
            <ul class="nav nav-primary nav-dotted nav-dot-separated justify-content-center justify-content-md-end">
                <li class="nav-item">
                    <a class="nav-link" href="javascript:void(0)">Contáctanos</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="#">FAQ</a>
                </li>
            </ul>
        </div>
        &copy; <script>document.write(new Date().getFullYear())</script> <a href="https://www.munisanmiguel.gob.pe/">Municipalidad de San Miguel</a>. Todos los derechos reservados.
    </footer>

    <!-- Modal Eliminar único -->
    <div class="modal fade" id="modalEliminar" tabindex="-1" aria-labelledby="modalEliminarLabel" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header bg-danger text-white">
                    <h5 class="modal-title" id="modalEliminarLabel">¿Estás seguro?</h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Cerrar"></button>
                </div>
                <div class="modal-body">
                    Esta acción eliminará el servicio de forma permanente. ¿Deseas continuar?
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancelar</button>
                    <button type="button" id="btnConfirmarEliminar" class="btn btn-danger">Eliminar</button>

                </div>
            </div>
        </div>
    </div>

    <!-- Modal Editar Servicio -->
    <div class="modal fade" id="modalEditar" tabindex="-1" aria-labelledby="modalEditarLabel" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header bg-primary text-white">
                    <h5 class="modal-title" id="modalEditarLabel">Editar Servicio</h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Cerrar"></button>
                </div>
                <div class="modal-body">
                    <form id="formEditarServicio">
                        <input type="hidden" id="editId" name="id" />

                        <div class="mb-3">
                            <label for="editTipo" class="form-label">Tipo de Servicio</label>
                            <input type="text" class="form-control" id="editTipo" name="tipoEspacioNombre" readonly>
                        </div>

                        <div class="mb-3">
                            <label for="editNombre" class="form-label">Nombre</label>
                            <input type="text" class="form-control" id="editNombre" name="nombre">
                        </div>

                        <div class="mb-3">
                            <label for="editDireccion" class="form-label">Ubicación</label>
                            <input type="text" class="form-control" id="editDireccion" name="ubicacion">
                        </div>

                        <div class="mb-3">
                            <label for="editCapacidad" class="form-label">Aforo</label>
                            <input type="number" class="form-control" id="editCapacidad" name="aforo">
                        </div>

                        <div class="mb-3">
                            <label for="editContacto" class="form-label">Correo de contacto</label>
                            <input type="email" class="form-control" id="editContacto" name="correoContacto">
                        </div>

                        <div class="row">
                            <div class="col">
                                <label for="editHoraAbre" class="form-label">Hora de apertura</label>
                                <input type="time" class="form-control" id="editHoraAbre" name="horaAbre">
                            </div>
                            <div class="col">
                                <label for="editHoraCierra" class="form-label">Hora de cierre</label>
                                <input type="time" class="form-control" id="editHoraCierra" name="horaCierra">
                            </div>
                        </div>
                    </form>

                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancelar</button>
                    <button type="button" class="btn btn-primary" onclick="guardarCambios()">Guardar</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal Programar Mantenimiento -->
    <div class="modal fade" id="modalMantenimiento" tabindex="-1" aria-labelledby="modalMantenimientoLabel" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header bg-info text-white">
                    <h5 class="modal-title" id="modalMantenimientoLabel">
                        <i class="mdi mdi-wrench me-2"></i>Programar Mantenimiento
                    </h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Cerrar"></button>
                </div>
                <div class="modal-body">
                    <form id="formMantenimiento">
                        <input type="hidden" id="mantenimientoId" name="id" />

                        <!-- Información del servicio (solo lectura) -->
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label for="mantenimientoTipo" class="form-label">Tipo de Servicio</label>
                                <input type="text" class="form-control" id="mantenimientoTipo" readonly>
                            </div>
                            <div class="col-md-6">
                                <label for="mantenimientoNombre" class="form-label">Nombre del Lugar</label>
                                <input type="text" class="form-control" id="mantenimientoNombre" readonly>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="mantenimientoUbicacion" class="form-label">Ubicación</label>
                            <input type="text" class="form-control" id="mantenimientoUbicacion" readonly>
                        </div>

                        <!-- Información del mantenimiento -->
                        <hr>
                        <h6 class="text-info mb-3">
                            <i class="mdi mdi-calendar-clock me-2"></i>Detalles del Mantenimiento
                        </h6>

                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label for="fechaMantenimiento" class="form-label">Fecha de Mantenimiento <span class="text-danger">*</span></label>
                                <input type="date" class="form-control" id="fechaMantenimiento" name="fechaMantenimiento" required>
                            </div>
                            <div class="col-md-6">
                                <label for="tipoMantenimiento" class="form-label">Tipo de Mantenimiento <span class="text-danger">*</span></label>
                                <select class="form-select" id="tipoMantenimiento" name="tipoMantenimiento" required>
                                    <option value="">Seleccione tipo</option>
                                    <option value="preventivo">🔧 Preventivo</option>
                                    <option value="correctivo">⚠️ Correctivo</option>
                                    <option value="limpieza">🧹 Limpieza Profunda</option>
                                    <option value="inspeccion">🔍 Inspección</option>
                                    <option value="reparacion">🔨 Reparación</option>
                                </select>
                            </div>
                        </div>

                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label for="horaInicioMantenimiento" class="form-label">Hora de Inicio <span class="text-danger">*</span></label>
                                <input type="time" class="form-control" id="horaInicioMantenimiento" name="horaInicio" required>
                            </div>
                            <div class="col-md-6">
                                <label for="horaFinMantenimiento" class="form-label">Hora de Fin <span class="text-danger">*</span></label>
                                <input type="time" class="form-control" id="horaFinMantenimiento" name="horaFin" required>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="responsableMantenimiento" class="form-label">Responsable <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="responsableMantenimiento" name="responsable"
                                   placeholder="Nombre del responsable del mantenimiento" required>
                        </div>

                        <div class="mb-3">
                            <label for="descripcionMantenimiento" class="form-label">Descripción del Trabajo <span class="text-danger">*</span></label>
                            <textarea class="form-control" id="descripcionMantenimiento" name="descripcion" rows="3"
                                      placeholder="Describa las actividades de mantenimiento a realizar..." required></textarea>
                        </div>

                        <div class="mb-3">
                            <label for="prioridadMantenimiento" class="form-label">Prioridad</label>
                            <select class="form-select" id="prioridadMantenimiento" name="prioridad">
                                <option value="baja">🟢 Baja</option>
                                <option value="media" selected>🟡 Media</option>
                                <option value="alta">🟠 Alta</option>
                                <option value="urgente">🔴 Urgente</option>
                            </select>
                        </div>

                        <div class="mb-3">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="suspenderServicio" name="suspenderServicio">
                                <label class="form-check-label" for="suspenderServicio">
                                    <strong>Suspender servicio durante el mantenimiento</strong>
                                    <small class="text-muted d-block">El servicio no estará disponible para reservas durante este período</small>
                                </label>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                        <i class="mdi mdi-close me-1"></i>Cancelar
                    </button>
                    <button type="button" class="btn btn-info" onclick="programarMantenimiento()">
                        <i class="mdi mdi-calendar-check me-1"></i>Programar Mantenimiento
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Add the sidebar's background. This div must be placed immediately after the control sidebar -->
    <div class="control-sidebar-bg"></div>

</div>
<!-- ./wrapper -->

<!-- ./side demo panel -->

<!-- Sidebar -->

<script>
    function cargarDatosModal(boton) {
        const fila = boton.closest('tr');
        const celdas = fila.querySelectorAll('td');
        const id = fila.getAttribute('data-id');

        // Llenar los campos del modal
        document.getElementById('editId').value = id;
        document.getElementById('editTipo').value = celdas[0].textContent.trim();
        document.getElementById('editNombre').value = celdas[1].textContent.trim();
        document.getElementById('editDireccion').value = celdas[2].textContent.trim();
        document.getElementById('editCapacidad').value = celdas[3].textContent.trim() === '-' ? '' : celdas[3].textContent.trim();

        // El horario ahora está en la columna 4 (índice 4)
        const horario = celdas[4].textContent.trim().split("-");
        document.getElementById('editHoraAbre').value = horario[0].trim();
        document.getElementById('editHoraCierra').value = horario[1].trim();

        fila.setAttribute('data-editar', 'true');
    }

    // Función para abrir el modal de mantenimiento
    function abrirMantenimiento(boton) {
        const fila = boton.closest('tr');
        const celdas = fila.querySelectorAll('td');
        const id = fila.getAttribute('data-id');

        // Llenar los campos de información del servicio (solo lectura)
        document.getElementById('mantenimientoId').value = id;
        document.getElementById('mantenimientoTipo').value = celdas[0].textContent.trim();
        document.getElementById('mantenimientoNombre').value = celdas[1].textContent.trim();
        document.getElementById('mantenimientoUbicacion').value = celdas[2].textContent.trim();

        // Limpiar los campos del formulario de mantenimiento
        document.getElementById('fechaMantenimiento').value = '';
        document.getElementById('tipoMantenimiento').value = '';
        document.getElementById('horaInicioMantenimiento').value = '';
        document.getElementById('horaFinMantenimiento').value = '';
        document.getElementById('responsableMantenimiento').value = '';
        document.getElementById('descripcionMantenimiento').value = '';
        document.getElementById('prioridadMantenimiento').value = 'media';
        document.getElementById('suspenderServicio').checked = false;

        // Establecer fecha mínima como hoy
        const hoy = new Date().toISOString().split('T')[0];
        document.getElementById('fechaMantenimiento').min = hoy;

        // Abrir el modal
        const modal = new bootstrap.Modal(document.getElementById('modalMantenimiento'));
        modal.show();
    }

    // Función para programar el mantenimiento
    function programarMantenimiento() {
        // Validar campos requeridos
        const campos = {
            fecha: document.getElementById('fechaMantenimiento').value,
            tipo: document.getElementById('tipoMantenimiento').value,
            horaInicio: document.getElementById('horaInicioMantenimiento').value,
            horaFin: document.getElementById('horaFinMantenimiento').value,
            responsable: document.getElementById('responsableMantenimiento').value.trim(),
            descripcion: document.getElementById('descripcionMantenimiento').value.trim()
        };

        // Validaciones
        if (!campos.fecha) {
            alert('Por favor seleccione la fecha del mantenimiento');
            return;
        }

        if (!campos.tipo) {
            alert('Por favor seleccione el tipo de mantenimiento');
            return;
        }

        if (!campos.horaInicio || !campos.horaFin) {
            alert('Por favor ingrese las horas de inicio y fin');
            return;
        }

        if (campos.horaInicio >= campos.horaFin) {
            alert('La hora de fin debe ser posterior a la hora de inicio');
            return;
        }

        if (!campos.responsable) {
            alert('Por favor ingrese el nombre del responsable');
            return;
        }

        if (!campos.descripcion) {
            alert('Por favor describa el trabajo a realizar');
            return;
        }

        // Recopilar todos los datos
        const datosMantenimiento = {
            servicioId: document.getElementById('mantenimientoId').value,
            servicioNombre: document.getElementById('mantenimientoNombre').value,
            servicioTipo: document.getElementById('mantenimientoTipo').value,
            servicioUbicacion: document.getElementById('mantenimientoUbicacion').value,
            fecha: campos.fecha,
            tipo: campos.tipo,
            horaInicio: campos.horaInicio,
            horaFin: campos.horaFin,
            responsable: campos.responsable,
            descripcion: campos.descripcion,
            prioridad: document.getElementById('prioridadMantenimiento').value,
            suspenderServicio: document.getElementById('suspenderServicio').checked
        };

        // Por ahora mostrar los datos (después puedes enviar al servidor)
        console.log('📋 Mantenimiento programado:', datosMantenimiento);

        // Mostrar confirmación
        const tipoTexto = document.getElementById('tipoMantenimiento').selectedOptions[0].textContent;
        const prioridadTexto = document.getElementById('prioridadMantenimiento').selectedOptions[0].textContent;

        alert(`✅ Mantenimiento programado exitosamente!\n\n` +
              `🏢 Servicio: ${datosMantenimiento.servicioNombre}\n` +
              `📅 Fecha: ${datosMantenimiento.fecha}\n` +
              `⏰ Horario: ${datosMantenimiento.horaInicio} - ${datosMantenimiento.horaFin}\n` +
              `🔧 Tipo: ${tipoTexto}\n` +
              `⚡ Prioridad: ${prioridadTexto}\n` +
              `👤 Responsable: ${datosMantenimiento.responsable}\n` +
              `${datosMantenimiento.suspenderServicio ? '⚠️ Servicio suspendido durante mantenimiento' : '✅ Servicio disponible durante mantenimiento'}`);

        // Cerrar el modal
        const modal = bootstrap.Modal.getInstance(document.getElementById('modalMantenimiento'));
        modal.hide();

        // TODO: Aquí puedes agregar la llamada al servidor para guardar el mantenimiento
        // fetch('/admin/programar-mantenimiento', { ... })
    }

    function guardarCambios() {
        const id = document.getElementById('editId').value;
        if (!id) return alert("ID no encontrado");

        const data = {
            id: parseInt(id),
            tipoEspacio: {
                nombre: document.getElementById('editTipo').value.trim()
            },
            nombre: document.getElementById('editNombre').value.trim(),
            ubicacion: document.getElementById('editDireccion').value.trim(),
            aforo: document.getElementById('editCapacidad').value ? parseInt(document.getElementById('editCapacidad').value.trim()) : null,
            correoContacto: document.getElementById('editContacto').value.trim(),
            horaAbre: document.getElementById('editHoraAbre').value,
            horaCierra: document.getElementById('editHoraCierra').value
        };

        const token = document.querySelector('meta[name="_csrf"]').getAttribute('content');
        const header = document.querySelector('meta[name="_csrf_header"]').getAttribute('content');

        fetch(`/admin/actualizar/${id}`, {
            method: 'PUT',
            headers: {
                'Content-Type': 'application/json',
                [header]: token
            },
            body: JSON.stringify(data)
        })
                .then(res => {
                if (!res.ok) throw new Error("Error al actualizar");
                return res.text();
            })
            .then(() => {
                const fila = document.querySelector(`tr[data-id="${id}"]`);
                const tabla = $('#tablaServicios').DataTable(); // Asegúrate de que tu tabla tiene ese ID
                const row = tabla.row(fila);

                const acciones = fila.querySelector('td:last-child').innerHTML;

                // Reemplazar datos de la fila (sin columna de contacto)
                row.data([
                    data.tipoEspacio.nombre,
                    data.nombre,
                    data.ubicacion,
                    data.aforo !== null ? data.aforo : '-',
                    `${data.horaAbre} - ${data.horaCierra}`,
                    acciones
                ]).draw(false);

                const modalElement = document.getElementById('modalEditar');
                const modalInstance = bootstrap.Modal.getOrCreateInstance(modalElement);
                modalInstance.hide();

                // 🧹 Forzar limpieza del backdrop si no desaparece
                document.querySelectorAll('.modal-backdrop').forEach(el => el.remove());
                document.body.classList.remove('modal-open');
                document.body.style = '';  // Quita estilos extra que pueden quedar
            })
            .catch(err => alert(err.message));
    }

</script>



<!-- Page Content overlay -->
<!-- Script de DataTables si no está activado -->
<!-- jQuery -->


<!-- Inicialización de DataTables -->
<script>
    document.addEventListener("DOMContentLoaded", function () {
        let idEliminar = null;

        $('#tablaServicios').on('click', '.delete-btn', function () {
            const fila = $(this).closest('tr');
            idEliminar = fila.attr('data-id');
            console.log("ID seleccionado para eliminar:", idEliminar);
        });

        const btnEliminar = document.getElementById('btnConfirmarEliminar');
        if (btnEliminar) {
            btnEliminar.addEventListener('click', () => {
                if (!idEliminar) return alert("No se ha seleccionado ningún servicio para eliminar");
                console.log("Llamando al DELETE con ID:", idEliminar);

                fetch(`/admin/eliminar/${idEliminar}`, {
                    method: 'DELETE'
                })
                    .then(res => {
                        if (!res.ok) throw new Error("Error al eliminar");

                        const tabla = $('#tablaServicios').DataTable();
                        tabla.row($(`tr[data-id="${idEliminar}"]`)).remove().draw(false);

                        idEliminar = null;
                        const modalElement = document.getElementById('modalEliminar');
                        let modalInstance = bootstrap.Modal.getInstance(modalElement);
                        if (!modalInstance) {
                            modalInstance = new bootstrap.Modal(modalElement);
                        }
                        modalInstance.hide();

                        document.querySelectorAll('.modal-backdrop').forEach(el => el.remove());
                        document.body.classList.remove('modal-open');
                        document.body.style = '';
                    })
                    .catch(err => alert(err.message));
            });
        } else {
            console.error("❌ No se encontró el botón #btnConfirmarEliminar");
        }
    });
</script>


<!-- Vendor JS -->
<script src="/js/vendors.min.js"></script>
<script src="/js/pages/chat-popup.js"></script>
<script src="/assets/icons/feather-icons/feather.min.js"></script>

<script src="/assets/vendor_components/datatable/datatables.min.js"></script>

<!-- Rhythm Admin App -->
<script src="/js/template.js"></script>
<script src="/js/pages/patients.js"></script>

<script>
    $(document).ready(function () {
        $('#tablaServicios').DataTable({
            pageLength: 5,
            lengthChange: false,
            info: false,
            language: {
                url: "//cdn.datatables.net/plug-ins/1.13.6/i18n/es-ES.json",
                search: "Buscar:",
                paginate: {
                    previous: "Anterior",
                    next: "Siguiente"
                }
            },
            dom: '<"datatable-header"f>rt<"datatable-footer"p><"clear">'
        });
    });
</script>

<script>
    feather.replace();
</script>


<script>
    document.addEventListener("DOMContentLoaded", function () {
        const loader = document.getElementById('loader');
        if (loader) {
            loader.style.display = 'none';  // Oculta el fondo negro
        }
    });
</script>




</body>
</html>
