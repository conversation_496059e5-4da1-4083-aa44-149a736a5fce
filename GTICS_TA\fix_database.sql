-- Script para arreglar problemas de la base de datos
-- Ejecutar este script para solucionar el problema de la clave primaria compuesta

USE gtics_2;

-- 1. <PERSON><PERSON>, eliminar las restricciones de clave foránea que dependen de la tabla espaciosdeportivos
ALTER TABLE `reservas` DROP FOREIGN KEY `fk_Reservas_EspaciosDeportivos1`;
ALTER TABLE `horarios` DROP FOREIGN KEY `fk_Horarios_EspaciosDeportivos1`;
ALTER TABLE `horarioscoordinador` DROP FOREIGN KEY `fk_horarioscoordinador_espaciosdeportivos1`;
ALTER TABLE `comentarios` DROP FOREIGN KEY `fk_comentarios_espaciosdeportivos1`;
ALTER TABLE `mantenimiento` DROP FOREIGN KEY `fk_mantenimiento_espaciosdeportivos1`;

-- 2. Eliminar las restricciones de las tablas específicas
ALTER TABLE `piscinas` DROP FOREIGN KEY `fk_piscinas_espaciosdeportivos1`;
ALTER TABLE `canchasfutbol` DROP FOREIGN KEY `fk_canchasfutbol_espaciosdeportivos1`;
ALTER TABLE `pistasatletismo` DROP FOREIGN KEY `fk_pistasatletismo_espaciosdeportivos1`;
ALTER TABLE `estadios` DROP FOREIGN KEY `fk_estadios_espaciosdeportivos1`;

-- 3. Eliminar la clave primaria compuesta actual
ALTER TABLE `espaciosdeportivos` DROP PRIMARY KEY;

-- 4. Crear nueva clave primaria simple
ALTER TABLE `espaciosdeportivos` ADD PRIMARY KEY (`id_espacio`);

-- 5. Hacer que id_lista_fotos sea opcional (puede ser NULL)
ALTER TABLE `espaciosdeportivos` MODIFY COLUMN `id_lista_fotos` INT NULL;

-- 6. Hacer que descripcion_larga sea opcional
ALTER TABLE `espaciosdeportivos` MODIFY COLUMN `descripcion_larga` TEXT NULL;

-- 7. Recrear las restricciones de clave foránea
ALTER TABLE `reservas` 
ADD CONSTRAINT `fk_Reservas_EspaciosDeportivos1`
    FOREIGN KEY (`id_espacio`)
    REFERENCES `espaciosdeportivos` (`id_espacio`)
    ON DELETE CASCADE
    ON UPDATE NO ACTION;

ALTER TABLE `horarios` 
ADD CONSTRAINT `fk_Horarios_EspaciosDeportivos1`
    FOREIGN KEY (`id_espacio`)
    REFERENCES `espaciosdeportivos` (`id_espacio`)
    ON DELETE CASCADE
    ON UPDATE NO ACTION;

ALTER TABLE `horarioscoordinador` 
ADD CONSTRAINT `fk_horarioscoordinador_espaciosdeportivos1`
    FOREIGN KEY (`id_espacio`)
    REFERENCES `espaciosdeportivos` (`id_espacio`)
    ON DELETE CASCADE
    ON UPDATE NO ACTION;

ALTER TABLE `comentarios` 
ADD CONSTRAINT `fk_comentarios_espaciosdeportivos1`
    FOREIGN KEY (`id_espacio`)
    REFERENCES `espaciosdeportivos` (`id_espacio`)
    ON DELETE CASCADE
    ON UPDATE NO ACTION;

ALTER TABLE `mantenimiento` 
ADD CONSTRAINT `fk_mantenimiento_espaciosdeportivos1`
    FOREIGN KEY (`id_espacio`)
    REFERENCES `espaciosdeportivos` (`id_espacio`)
    ON DELETE CASCADE
    ON UPDATE NO ACTION;

-- 8. Recrear las restricciones de las tablas específicas
ALTER TABLE `piscinas` 
ADD CONSTRAINT `fk_piscinas_espaciosdeportivos1`
    FOREIGN KEY (`id_espacio`)
    REFERENCES `espaciosdeportivos` (`id_espacio`)
    ON DELETE CASCADE
    ON UPDATE NO ACTION;

ALTER TABLE `canchasfutbol` 
ADD CONSTRAINT `fk_canchasfutbol_espaciosdeportivos1`
    FOREIGN KEY (`id_espacio`)
    REFERENCES `espaciosdeportivos` (`id_espacio`)
    ON DELETE CASCADE
    ON UPDATE NO ACTION;

ALTER TABLE `pistasatletismo` 
ADD CONSTRAINT `fk_pistasatletismo_espaciosdeportivos1`
    FOREIGN KEY (`id_espacio`)
    REFERENCES `espaciosdeportivos` (`id_espacio`)
    ON DELETE CASCADE
    ON UPDATE NO ACTION;

ALTER TABLE `estadios` 
ADD CONSTRAINT `fk_estadios_espaciosdeportivos1`
    FOREIGN KEY (`id_espacio`)
    REFERENCES `espaciosdeportivos` (`id_espacio`)
    ON DELETE CASCADE
    ON UPDATE NO ACTION;

-- 9. Recrear la restricción de lista de fotos (opcional)
ALTER TABLE `espaciosdeportivos` 
ADD CONSTRAINT `fk_EspaciosDeportivos_ListaFotos1`
    FOREIGN KEY (`id_lista_fotos`)
    REFERENCES `listafotos` (`id_lista_fotos`)
    ON DELETE SET NULL
    ON UPDATE NO ACTION;

-- 10. Verificar la estructura final
DESCRIBE espaciosdeportivos;

SELECT 'Script ejecutado correctamente. La tabla espaciosdeportivos ahora tiene una clave primaria simple.' AS resultado;
