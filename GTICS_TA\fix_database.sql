-- Script para arreglar problemas de la base de datos (VERSIÓN SEGURA)
-- Ejecutar este script para solucionar el problema de la clave primaria compuesta

USE gtics_2;

-- <PERSON><PERSON>, vamos a ver qué restricciones existen realmente
SELECT
    TABLE_NAME,
    CONSTRAINT_NAME,
    CONSTRAINT_TYPE
FROM INFORMATION_SCHEMA.TABLE_CONSTRAINTS
WHERE TABLE_SCHEMA = 'gtics_2'
AND TABLE_NAME IN ('espaciosdeportivos', 'reservas', 'horarios', 'horarioscoordinador', 'comentarios', 'mantenimiento', 'piscinas', 'canchasfutbol', 'pistasatletismo', 'estadios')
AND CONSTRAINT_TYPE = 'FOREIGN KEY';

-- 1. Eliminar restricciones de clave foránea de forma segura (solo si existen)
-- Para reservas
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.TABLE_CONSTRAINTS
     WHERE TABLE_SCHEMA = 'gtics_2' AND TABLE_NAME = 'reservas' AND CONSTRAINT_NAME = 'fk_Reservas_EspaciosDeportivos1') > 0,
    'ALTER TABLE `reservas` DROP FOREIGN KEY `fk_Reservas_EspaciosDeportivos1`',
    'SELECT "FK fk_Reservas_EspaciosDeportivos1 no existe" as mensaje'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- Para horarios
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.TABLE_CONSTRAINTS
     WHERE TABLE_SCHEMA = 'gtics_2' AND TABLE_NAME = 'horarios' AND CONSTRAINT_NAME = 'fk_Horarios_EspaciosDeportivos1') > 0,
    'ALTER TABLE `horarios` DROP FOREIGN KEY `fk_Horarios_EspaciosDeportivos1`',
    'SELECT "FK fk_Horarios_EspaciosDeportivos1 no existe" as mensaje'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 3. Eliminar la clave primaria compuesta actual
ALTER TABLE `espaciosdeportivos` DROP PRIMARY KEY;

-- 4. Crear nueva clave primaria simple
ALTER TABLE `espaciosdeportivos` ADD PRIMARY KEY (`id_espacio`);

-- 5. Hacer que id_lista_fotos sea opcional (puede ser NULL)
ALTER TABLE `espaciosdeportivos` MODIFY COLUMN `id_lista_fotos` INT NULL;

-- 6. Hacer que descripcion_larga sea opcional
ALTER TABLE `espaciosdeportivos` MODIFY COLUMN `descripcion_larga` TEXT NULL;

-- 7. Recrear las restricciones de clave foránea
ALTER TABLE `reservas` 
ADD CONSTRAINT `fk_Reservas_EspaciosDeportivos1`
    FOREIGN KEY (`id_espacio`)
    REFERENCES `espaciosdeportivos` (`id_espacio`)
    ON DELETE CASCADE
    ON UPDATE NO ACTION;

ALTER TABLE `horarios` 
ADD CONSTRAINT `fk_Horarios_EspaciosDeportivos1`
    FOREIGN KEY (`id_espacio`)
    REFERENCES `espaciosdeportivos` (`id_espacio`)
    ON DELETE CASCADE
    ON UPDATE NO ACTION;

ALTER TABLE `horarioscoordinador` 
ADD CONSTRAINT `fk_horarioscoordinador_espaciosdeportivos1`
    FOREIGN KEY (`id_espacio`)
    REFERENCES `espaciosdeportivos` (`id_espacio`)
    ON DELETE CASCADE
    ON UPDATE NO ACTION;

ALTER TABLE `comentarios` 
ADD CONSTRAINT `fk_comentarios_espaciosdeportivos1`
    FOREIGN KEY (`id_espacio`)
    REFERENCES `espaciosdeportivos` (`id_espacio`)
    ON DELETE CASCADE
    ON UPDATE NO ACTION;

ALTER TABLE `mantenimiento` 
ADD CONSTRAINT `fk_mantenimiento_espaciosdeportivos1`
    FOREIGN KEY (`id_espacio`)
    REFERENCES `espaciosdeportivos` (`id_espacio`)
    ON DELETE CASCADE
    ON UPDATE NO ACTION;

-- 8. Recrear las restricciones de las tablas específicas
ALTER TABLE `piscinas` 
ADD CONSTRAINT `fk_piscinas_espaciosdeportivos1`
    FOREIGN KEY (`id_espacio`)
    REFERENCES `espaciosdeportivos` (`id_espacio`)
    ON DELETE CASCADE
    ON UPDATE NO ACTION;

ALTER TABLE `canchasfutbol` 
ADD CONSTRAINT `fk_canchasfutbol_espaciosdeportivos1`
    FOREIGN KEY (`id_espacio`)
    REFERENCES `espaciosdeportivos` (`id_espacio`)
    ON DELETE CASCADE
    ON UPDATE NO ACTION;

ALTER TABLE `pistasatletismo` 
ADD CONSTRAINT `fk_pistasatletismo_espaciosdeportivos1`
    FOREIGN KEY (`id_espacio`)
    REFERENCES `espaciosdeportivos` (`id_espacio`)
    ON DELETE CASCADE
    ON UPDATE NO ACTION;

ALTER TABLE `estadios` 
ADD CONSTRAINT `fk_estadios_espaciosdeportivos1`
    FOREIGN KEY (`id_espacio`)
    REFERENCES `espaciosdeportivos` (`id_espacio`)
    ON DELETE CASCADE
    ON UPDATE NO ACTION;

-- 9. Recrear la restricción de lista de fotos (opcional)
ALTER TABLE `espaciosdeportivos` 
ADD CONSTRAINT `fk_EspaciosDeportivos_ListaFotos1`
    FOREIGN KEY (`id_lista_fotos`)
    REFERENCES `listafotos` (`id_lista_fotos`)
    ON DELETE SET NULL
    ON UPDATE NO ACTION;

-- 10. Verificar la estructura final
DESCRIBE espaciosdeportivos;

SELECT 'Script ejecutado correctamente. La tabla espaciosdeportivos ahora tiene una clave primaria simple.' AS resultado;
