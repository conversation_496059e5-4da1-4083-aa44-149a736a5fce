package com.example.gtics_ta.Services;

import com.example.gtics_ta.DTO.AsignacionHorarioDTO;
import com.example.gtics_ta.DTO.CalendarioHorarioDTO;
import com.example.gtics_ta.Entity.*;
import com.example.gtics_ta.Repository.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.sql.Time;
import java.time.DayOfWeek;
import java.time.LocalDate;
import java.time.LocalTime;
import java.time.temporal.TemporalAdjusters;
import java.util.*;
import java.util.stream.Collectors;

@Service
public class HorarioCoordinadorService {

    @Autowired
    private HorariosCoordinadorRepository horariosCoordinadorRepository;

    @Autowired
    private UsuarioRepository usuarioRepository;

    @Autowired
    private EspaciosDeportivosRepository espaciosDeportivosRepository;

    @Autowired
    private MantenimientoRepository mantenimientoRepository;

    // Obtener todos los coordinadores activos
    public List<Usuario> obtenerCoordinadoresActivos() {
        return usuarioRepository.findByRol_IdRol(2) // Rol coordinador = 2
                .stream()
                .filter(Usuario::isActivo)
                .collect(Collectors.toList());
    }

    // Obtener todos los espacios deportivos operativos
    public List<EspaciosDeportivos> obtenerEspaciosOperativos() {
        return espaciosDeportivosRepository.findAll()
                .stream()
                .filter(EspaciosDeportivos::isOperativo)
                .collect(Collectors.toList());
    }

    // Validar si se puede asignar un horario (sin traslapes ni mantenimiento)
    public String validarAsignacionHorario(AsignacionHorarioDTO dto) {
        Usuario coordinador = usuarioRepository.findById(dto.getCoordinadorId()).orElse(null);
        EspaciosDeportivos espacio = espaciosDeportivosRepository.findById(dto.getEspacioId()).orElse(null);

        if (coordinador == null) {
            return "Coordinador no encontrado";
        }

        if (espacio == null) {
            return "Espacio deportivo no encontrado";
        }

        if (!espacio.isOperativo()) {
            return "El espacio deportivo no está operativo";
        }

        // Validar horarios (hora entrada debe ser menor que hora salida)
        if (dto.getHoraEntrada().isAfter(dto.getHoraSalida()) || dto.getHoraEntrada().equals(dto.getHoraSalida())) {
            return "La hora de entrada debe ser anterior a la hora de salida";
        }

        // Validar fechas
        if (dto.getFechaInicio().isAfter(dto.getFechaFin())) {
            return "La fecha de inicio debe ser anterior o igual a la fecha de fin";
        }

        // Verificar mantenimiento en el espacio
        List<Mantenimiento> mantenimientos = mantenimientoRepository.findConflictosMantenimiento(
                espacio, dto.getFechaInicio(), dto.getFechaFin());
        
        if (!mantenimientos.isEmpty()) {
            return "El espacio tiene mantenimiento programado en las fechas seleccionadas: " +
                   mantenimientos.stream()
                           .map(m -> m.getFechaInicio() + " - " + m.getFechaFin())
                           .collect(Collectors.joining(", "));
        }

        // Verificar traslapes de horarios
        List<HorariosCoordinador> traslapes = horariosCoordinadorRepository.findTraslapeHorarios(
                coordinador, espacio,
                java.sql.Date.valueOf(dto.getFechaInicio()),
                java.sql.Date.valueOf(dto.getFechaFin()),
                Time.valueOf(dto.getHoraEntrada()),
                Time.valueOf(dto.getHoraSalida())
        );

        if (!traslapes.isEmpty()) {
            return "Ya existe un horario asignado que se traslapa con el horario propuesto";
        }

        return null; // Sin errores
    }

    // Asignar horario a coordinador
    public boolean asignarHorario(AsignacionHorarioDTO dto) {
        String validacion = validarAsignacionHorario(dto);
        if (validacion != null) {
            return false;
        }

        try {
            Usuario coordinador = usuarioRepository.findById(dto.getCoordinadorId()).get();
            EspaciosDeportivos espacio = espaciosDeportivosRepository.findById(dto.getEspacioId()).get();

            HorariosCoordinador horario = new HorariosCoordinador();
            horario.setUsuario(coordinador);
            horario.setEspacio(espacio);
            horario.setHoraEntrada(Time.valueOf(dto.getHoraEntrada()));
            horario.setHoraSalida(Time.valueOf(dto.getHoraSalida()));
            horario.setFechaInicio(java.sql.Date.valueOf(dto.getFechaInicio()));
            horario.setFechaFin(java.sql.Date.valueOf(dto.getFechaFin()));

            horariosCoordinadorRepository.save(horario);
            return true;
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }
    }

    // Obtener horarios para el calendario
    public List<CalendarioHorarioDTO> obtenerHorariosParaCalendario(Integer coordinadorId, LocalDate fechaInicio, LocalDate fechaFin) {
        List<HorariosCoordinador> horarios;
        
        if (coordinadorId != null) {
            Usuario coordinador = usuarioRepository.findById(coordinadorId).orElse(null);
            if (coordinador == null) return new ArrayList<>();
            
            horarios = horariosCoordinadorRepository.findHorariosCoordinadorEnSemana(
                    coordinador,
                    java.sql.Date.valueOf(fechaInicio),
                    java.sql.Date.valueOf(fechaFin)
            );
        } else {
            horarios = horariosCoordinadorRepository.findAllHorariosEnSemana(
                    java.sql.Date.valueOf(fechaInicio),
                    java.sql.Date.valueOf(fechaFin)
            );
        }

        return horarios.stream().map(this::convertirACalendarioDTO).collect(Collectors.toList());
    }

    // Convertir HorariosCoordinador a CalendarioHorarioDTO
    private CalendarioHorarioDTO convertirACalendarioDTO(HorariosCoordinador horario) {
        LocalDate fechaInicio = new java.sql.Date(horario.getFechaInicio().getTime()).toLocalDate();
        LocalDate fechaFin = new java.sql.Date(horario.getFechaFin().getTime()).toLocalDate();
        LocalTime horaEntrada = horario.getHoraEntrada().toLocalTime();
        LocalTime horaSalida = horario.getHoraSalida().toLocalTime();

        String titulo = String.format("%s - %s (%s - %s)",
                horario.getEspacio().getNombre(),
                horario.getUsuario().getNombres() + " " + horario.getUsuario().getApellidos(),
                horaEntrada.toString(),
                horaSalida.toString()
        );

        CalendarioHorarioDTO dto = new CalendarioHorarioDTO(
                horario.getId(),
                titulo,
                fechaInicio,
                fechaFin,
                horaEntrada,
                horaSalida,
                horario.getEspacio().getNombre(),
                horario.getEspacio().getTipoEspacio().getNombre(),
                horario.getUsuario().getNombres() + " " + horario.getUsuario().getApellidos()
        );

        return dto;
    }

    // Obtener fechas de la semana actual
    public Map<String, LocalDate> obtenerFechasSemanaActual() {
        LocalDate hoy = LocalDate.now();
        LocalDate lunes = hoy.with(TemporalAdjusters.previousOrSame(DayOfWeek.MONDAY));
        LocalDate domingo = hoy.with(TemporalAdjusters.nextOrSame(DayOfWeek.SUNDAY));

        Map<String, LocalDate> fechas = new HashMap<>();
        fechas.put("inicio", lunes);
        fechas.put("fin", domingo);
        return fechas;
    }

    // Eliminar horario
    public boolean eliminarHorario(Integer horarioId) {
        try {
            horariosCoordinadorRepository.deleteById(horarioId);
            return true;
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }
    }
}
