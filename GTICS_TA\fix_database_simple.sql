-- Script simple y seguro para arreglar la base de datos
-- Ejecutar paso a paso para evitar errores

USE gtics_2;

-- PASO 1: Ver qué restricciones existen actualmente
SELECT 
    TABLE_NAME,
    CONSTRAINT_NAME,
    CONSTRAINT_TYPE
FROM INFORMATION_SCHEMA.TABLE_CONSTRAINTS 
WHERE TABLE_SCHEMA = 'gtics_2' 
AND TABLE_NAME = 'espaciosdeportivos'
ORDER BY TABLE_NAME, CONSTRAINT_TYPE;

-- PASO 2: Ver la estructura actual de la tabla espaciosdeportivos
DESCRIBE espaciosdeportivos;

-- PASO 3: Hacer que id_lista_fotos sea opcional (esto es lo más importante)
ALTER TABLE `espaciosdeportivos` MODIFY COLUMN `id_lista_fotos` INT NULL;

-- PASO 4: Hacer que descripcion_larga sea opcional
ALTER TABLE `espaciosdeportivos` MODIFY COLUMN `descripcion_larga` TEXT NULL;

-- PASO 5: Verificar los cambios
DESCRIBE espaciosdeportivos;

SELECT 'Cambios aplicados correctamente. Ahora id_lista_fotos y descripcion_larga pueden ser NULL.' AS resultado;
