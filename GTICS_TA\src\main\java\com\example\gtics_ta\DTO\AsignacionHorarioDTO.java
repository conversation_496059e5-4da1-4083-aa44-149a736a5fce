package com.example.gtics_ta.DTO;

import java.time.LocalDate;
import java.time.LocalTime;
import java.util.List;

public class AsignacionHorarioDTO {
    private Integer coordinadorId;
    private String coordinadorNombre;
    private LocalDate fechaInicio;
    private LocalDate fechaFin;
    private List<Integer> espaciosSeleccionados;
    private String diaSemana; // LUNES, MARTES, etc.
    private LocalTime horaEntrada;
    private LocalTime horaSalida;
    private Integer espacioId;
    private String espacioNombre;
    private String tipoEspacio;

    // Constructores
    public AsignacionHorarioDTO() {}

    public AsignacionHorarioDTO(Integer coordinadorId, String coordinadorNombre) {
        this.coordinadorId = coordinadorId;
        this.coordinadorNombre = coordinadorNombre;
    }

    // Getters y Setters
    public Integer getCoordinadorId() {
        return coordinadorId;
    }

    public void setCoordinadorId(Integer coordinadorId) {
        this.coordinadorId = coordinadorId;
    }

    public String getCoordinadorNombre() {
        return coordinadorNombre;
    }

    public void setCoordinadorNombre(String coordinadorNombre) {
        this.coordinadorNombre = coordinadorNombre;
    }

    public LocalDate getFechaInicio() {
        return fechaInicio;
    }

    public void setFechaInicio(LocalDate fechaInicio) {
        this.fechaInicio = fechaInicio;
    }

    public LocalDate getFechaFin() {
        return fechaFin;
    }

    public void setFechaFin(LocalDate fechaFin) {
        this.fechaFin = fechaFin;
    }

    public List<Integer> getEspaciosSeleccionados() {
        return espaciosSeleccionados;
    }

    public void setEspaciosSeleccionados(List<Integer> espaciosSeleccionados) {
        this.espaciosSeleccionados = espaciosSeleccionados;
    }

    public String getDiaSemana() {
        return diaSemana;
    }

    public void setDiaSemana(String diaSemana) {
        this.diaSemana = diaSemana;
    }

    public LocalTime getHoraEntrada() {
        return horaEntrada;
    }

    public void setHoraEntrada(LocalTime horaEntrada) {
        this.horaEntrada = horaEntrada;
    }

    public LocalTime getHoraSalida() {
        return horaSalida;
    }

    public void setHoraSalida(LocalTime horaSalida) {
        this.horaSalida = horaSalida;
    }

    public Integer getEspacioId() {
        return espacioId;
    }

    public void setEspacioId(Integer espacioId) {
        this.espacioId = espacioId;
    }

    public String getEspacioNombre() {
        return espacioNombre;
    }

    public void setEspacioNombre(String espacioNombre) {
        this.espacioNombre = espacioNombre;
    }

    public String getTipoEspacio() {
        return tipoEspacio;
    }

    public void setTipoEspacio(String tipoEspacio) {
        this.tipoEspacio = tipoEspacio;
    }
}
