package com.example.gtics_ta.Repository;

import com.example.gtics_ta.Entity.Mantenimiento;
import com.example.gtics_ta.Entity.EspaciosDeportivos;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDate;
import java.util.Date;
import java.util.List;

@Repository
public interface MantenimientoRepository extends JpaRepository<Mantenimiento, Integer> {

    // Buscar mantenimientos por espacio deportivo
    List<Mantenimiento> findByEspacioOrderByFechaInicioDesc(EspaciosDeportivos espacio);

    // Buscar mantenimientos activos (programados o en progreso)
    @Query("SELECT m FROM Mantenimiento m WHERE m.estadoMantenimiento IN ('PROGRAMADO', 'EN_PROGRESO') ORDER BY m.fechaInicio")
    List<Mantenimiento> findMantenimientosActivos();

    // Verificar si un espacio tiene mantenimiento en una fecha específica
    @Query("SELECT m FROM Mantenimiento m WHERE m.espacio = :espacio AND " +
           ":fecha BETWEEN m.fechaInicio AND m.fechaFin AND " +
           "m.estadoMantenimiento IN ('PROGRAMADO', 'EN_PROGRESO')")
    List<Mantenimiento> findMantenimientoEnFecha(@Param("espacio") EspaciosDeportivos espacio, 
                                                 @Param("fecha") LocalDate fecha);

    // Verificar si un espacio tiene mantenimiento en un rango de fechas
    @Query("SELECT m FROM Mantenimiento m WHERE m.espacio = :espacio AND " +
           "((m.fechaInicio <= :fechaFin AND m.fechaFin >= :fechaInicio)) AND " +
           "m.estadoMantenimiento IN ('PROGRAMADO', 'EN_PROGRESO')")
    List<Mantenimiento> findMantenimientoEnRango(@Param("espacio") EspaciosDeportivos espacio,
                                                 @Param("fechaInicio") LocalDate fechaInicio,
                                                 @Param("fechaFin") LocalDate fechaFin);

    // Obtener mantenimientos por estado
    @Query("SELECT m FROM Mantenimiento m WHERE m.estadoMantenimiento = :estado ORDER BY m.fechaInicio")
    List<Mantenimiento> findByEstadoMantenimiento(@Param("estado") Mantenimiento.EstadoMantenimiento estado);

    // Obtener mantenimientos que requieren cierre total del espacio
    @Query("SELECT m FROM Mantenimiento m WHERE m.requiereCierreTotal = true AND " +
           "m.estadoMantenimiento IN ('PROGRAMADO', 'EN_PROGRESO') ORDER BY m.fechaInicio")
    List<Mantenimiento> findMantenimientosConCierreTotal();

    // Verificar conflictos de mantenimiento para asignación de horarios
    @Query("SELECT m FROM Mantenimiento m WHERE m.espacio = :espacio AND " +
           "m.estadoMantenimiento IN ('PROGRAMADO', 'EN_PROGRESO') AND " +
           "((m.fechaInicio <= :fechaFin AND m.fechaFin >= :fechaInicio))")
    List<Mantenimiento> findConflictosMantenimiento(@Param("espacio") EspaciosDeportivos espacio,
                                                    @Param("fechaInicio") LocalDate fechaInicio,
                                                    @Param("fechaFin") LocalDate fechaFin);
}
