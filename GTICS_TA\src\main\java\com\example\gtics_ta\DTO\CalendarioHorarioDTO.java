package com.example.gtics_ta.DTO;

import java.time.LocalDate;
import java.time.LocalTime;

public class CalendarioHorarioDTO {
    private Integer id;
    private String title;
    private LocalDate start;
    private LocalDate end;
    private LocalTime startTime;
    private LocalTime endTime;
    private String color;
    private String espacioNombre;
    private String tipoEspacio;
    private String coordinadorNombre;
    private boolean enMantenimiento;

    // Constructores
    public CalendarioHorarioDTO() {}

    public CalendarioHorarioDTO(Integer id, String title, LocalDate start, LocalDate end, 
                               LocalTime startTime, LocalTime endTime, String espacioNombre, 
                               String tipoEspacio, String coordinadorNombre) {
        this.id = id;
        this.title = title;
        this.start = start;
        this.end = end;
        this.startTime = startTime;
        this.endTime = endTime;
        this.espacioNombre = espacioNombre;
        this.tipoEspacio = tipoEspacio;
        this.coordinadorNombre = coordinadorNombre;
        this.color = determinarColor(tipoEspacio);
    }

    private String determinarColor(String tipoEspacio) {
        if (tipoEspacio == null) return "#6c757d";
        
        switch (tipoEspacio.toLowerCase()) {
            case "piscina":
                return "#007bff"; // Azul
            case "cancha de fútbol":
                return "#28a745"; // Verde
            case "pista de atletismo":
                return "#ffc107"; // Amarillo
            case "estadio":
                return "#dc3545"; // Rojo
            default:
                return "#6c757d"; // Gris
        }
    }

    // Getters y Setters
    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public LocalDate getStart() {
        return start;
    }

    public void setStart(LocalDate start) {
        this.start = start;
    }

    public LocalDate getEnd() {
        return end;
    }

    public void setEnd(LocalDate end) {
        this.end = end;
    }

    public LocalTime getStartTime() {
        return startTime;
    }

    public void setStartTime(LocalTime startTime) {
        this.startTime = startTime;
    }

    public LocalTime getEndTime() {
        return endTime;
    }

    public void setEndTime(LocalTime endTime) {
        this.endTime = endTime;
    }

    public String getColor() {
        return color;
    }

    public void setColor(String color) {
        this.color = color;
    }

    public String getEspacioNombre() {
        return espacioNombre;
    }

    public void setEspacioNombre(String espacioNombre) {
        this.espacioNombre = espacioNombre;
    }

    public String getTipoEspacio() {
        return tipoEspacio;
    }

    public void setTipoEspacio(String tipoEspacio) {
        this.tipoEspacio = tipoEspacio;
        this.color = determinarColor(tipoEspacio);
    }

    public String getCoordinadorNombre() {
        return coordinadorNombre;
    }

    public void setCoordinadorNombre(String coordinadorNombre) {
        this.coordinadorNombre = coordinadorNombre;
    }

    public boolean isEnMantenimiento() {
        return enMantenimiento;
    }

    public void setEnMantenimiento(boolean enMantenimiento) {
        this.enMantenimiento = enMantenimiento;
    }
}
