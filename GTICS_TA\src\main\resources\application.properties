spring.application.name=GTICS_TA
spring.datasource.url=***********************************
spring.datasource.username=root
spring.datasource.password=root
spring.datasource.driver-class-name=com.mysql.cj.jdbc.Driver
spring.jpa.hibernate.ddl-auto=update
spring.jpa.show-sql=true
spring.jpa.properties.hibernate.format_sql=true
spring.jpa.properties.hibernate.dialect=org.hibernate.dialect.MySQL8Dialect
spring.jpa.hibernate.naming.physical-strategy=org.hibernate.boot.model.naming.PhysicalNamingStrategyStandardImpl
spring.jpa.open-in-view=false
spring.thymeleaf.suffix=.html
spring.thymeleaf.mode=HTML
spring.thymeleaf.encoding=UTF-8
spring.thymeleaf.servlet.content-type=text/html
spring.thymeleaf.cache=false
spring.thymeleaf.prefix=classpath:/templates/

# Configuración de Google Maps
# Para desarrollo: usar la API key de prueba
# Para producción: la empresa debe reemplazar con su API key
google.maps.api.key=${GOOGLE_MAPS_API_KEY:AIzaSyB8gglF9Vdl-dlam3umZipOmq92nPlCEng}
google.maps.enabled=${GOOGLE_MAPS_ENABLED:true}
spring.thymeleaf.check-template-location=true
spring.thymeleaf.enabled=true
spring.thymeleaf.template-resolver-order=1
spring.session.store-type=jdbc
spring.session.jdbc.initialize-schema=always
spring.mail.host=smtp.gmail.com
spring.mail.port=587
spring.mail.username=<EMAIL>
spring.mail.password=cgxu gnnj hhus tuhy
spring.mail.properties.mail.smtp.auth=true
spring.mail.properties.mail.smtp.starttls.enable=true

# Configuración de logging para debug
logging.level.org.springframework.security=DEBUG
logging.level.org.springframework.security.web=DEBUG
logging.level.org.springframework.security.authentication=DEBUG
logging.level.com.example.gtics_ta=DEBUG
logging.level.org.springframework.jdbc=DEBUG
#
aws.access.key.id=********************
aws.secret.access.key=tvjyYRja9RQVI5dksIhmTZqzg86nMc9GQIQEfGJZ
aws.session.token=IQoJb3JpZ2luX2VjEMz//////////wEaCXVzLXdlc3QtMiJHMEUCIQDQh1ID0FFkvhZKfPPLGhIqcg16e9X1iJw40BulqzLRzQIgQmQOXiVRBD/sivs5ZjsXZbwq4/BoD7O4PExa4M3gQ+AqsQIItf//////////ARAAGgw3MzAzMzU1MTMxODciDO4qr5Aae7qiaOlYASqFAhEW8NblevBQRoO8zDskaTZOxJ4vCc94fJPciz9RM6HQesPILDtHNkbZi2bBC63rapHGrYnCiIUd5Uu+N69HwNQiQjcxyDot784Kah8drIgGc+ZDmWf+lfIsR3lPmRBcLtHU8+J36oh8vAscJUYd2Vy/Z9eXK/Efn74cuPRTm1D6RENhc2kp5tPEEKWW+/5FRuVtWEF9zzNfr+nfCx7u5dolbzqn/PoYTPL31Gwb+pISZeBdDMjgzxH53z9+S1tJNGtK89yljIcob5+IU90oQJcPVKaWDPoOl6cQ53voQFlBURHjki1Zh+4XJSEXDHWYMNuPeoRL5VLnCt9l5roQbrDTj6F1cjCDtdPCBjqdASOVuzLIsojgFFvGDTukE2TG91Z6QZ/2tPbuVk5/JemUWNaij9pUFe+uipnUSPpW23paWf+0kckYH1l5mziV1uzzbHr6tP+pqdPqwcCRrW0g9cF/qEvoWD8dZK+9h1TuX8/D78bqoM0bRG6hIQMhpBIjzziT0NjjCbJ5oTbhwVi765trcUUCCcfcVxvbvT30qV8zFh7qN0JeOpiYQyE= 
aws.s3.region=us-east-1
aws.s3.bucket.name=gtics-images-bucket-2025
aws.s3.url.expiration.minutes=60

#
openai.api.key=********************************************************************************************************************************************************************