<!DOCTYPE html>
<html lang="es" xmlns:th="http://www.thymeleaf.org">
<head>
  <meta charset="utf-8">
  <meta http-equiv="X-UA-Compatible" content="IE=edge">
  <meta name="viewport" content="width=device-width, initial-scale=1">
  <meta name="description" content="">
  <meta name="author" content="">
  <link rel="icon" href="../images/favicon.ico">

  <title>Asignar Horarios - Admin</title>

  <!-- Vendors Style-->
  <link rel="stylesheet" th:href="@{/css/vendors_css.css}">

  <!-- Style-->
  <link rel="stylesheet" th:href="@{/css/style.css}">
  <link rel="stylesheet" th:href="@{/css/skin_color.css}">

  <!-- FullCalendar CSS -->
  <link href='https://cdn.jsdelivr.net/npm/fullcalendar@6.1.8/main.min.css' rel='stylesheet' />

  <style>
    .espacio-card {
      border: 2px solid #e9ecef;
      border-radius: 8px;
      padding: 15px;
      margin: 10px 0;
      cursor: pointer;
      transition: all 0.3s ease;
    }
    
    .espacio-card:hover {
      border-color: #007bff;
      box-shadow: 0 2px 8px rgba(0,123,255,0.2);
    }
    
    .espacio-card.selected {
      border-color: #007bff;
      background-color: #f8f9ff;
    }
    
    .horario-form {
      background: #f8f9fa;
      border-radius: 8px;
      padding: 20px;
      margin-top: 20px;
    }
    
    .calendario-container {
      background: white;
      border-radius: 8px;
      padding: 20px;
      box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }
    
    .btn-dia {
      margin: 2px;
      min-width: 80px;
    }
    
    .alert-custom {
      border-radius: 8px;
      border: none;
      padding: 15px 20px;
    }
  </style>
</head>

<body class="hold-transition light-skin sidebar-mini theme-primary fixed">

<div class="wrapper">
  <header class="main-header">
    <div class="d-flex align-items-center justify-content-between px-20 py-10">
      <a href="#" class="logo d-flex align-items-center">
        <img src="/images/logo.png" alt="Logo" class="me-2" style="height: 40px;">
        <span class="fs-20 fw-700 text-primary">Admin Panel</span>
      </a>
      <div th:replace="~{fragments :: navbarAdmin}"></div>
    </div>
  </header>
  <div th:replace="~{fragments :: sideBarAdmin}"></div>

  <!-- Content Wrapper -->
  <div class="content-wrapper">
    <div class="container-full">
      <!-- Content Header -->
      <div class="content-header">
        <div class="d-flex align-items-center">
          <div class="me-auto">
            <h4 class="page-title">Asignar Horarios</h4>
            <div class="d-inline-block align-items-center">
              <nav>
                <ol class="breadcrumb">
                  <li class="breadcrumb-item"><a href="#"><i class="mdi mdi-home-outline"></i></a></li>
                  <li class="breadcrumb-item" aria-current="page">Admin</li>
                  <li class="breadcrumb-item active" aria-current="page">Asignar Horarios</li>
                </ol>
              </nav>
            </div>
          </div>
        </div>
      </div>

      <!-- Main content -->
      <section class="content">
        <div class="row">
          <!-- Formulario de Asignación -->
          <div class="col-lg-8">
            <div class="box">
              <div class="box-header with-border">
                <h4 class="box-title">
                  <i class="fa fa-calendar-plus-o me-2"></i>Asignar Horarios a Coordinadores
                </h4>
              </div>
              <div class="box-body">
                <!-- Selección de Coordinador y Semana -->
                <div class="row">
                  <div class="col-md-6">
                    <div class="form-group">
                      <label class="form-label">Coordinador</label>
                      <select id="coordinadorSelect" class="form-select">
                        <option value="">Seleccione un coordinador</option>
                        <option th:each="coordinador : ${coordinadores}"
                                th:value="${coordinador.id}"
                                th:text="${coordinador.nombres + ' ' + coordinador.apellidos}">
                        </option>
                      </select>
                    </div>
                  </div>
                  <div class="col-md-3">
                    <div class="form-group">
                      <label class="form-label">Fecha Inicio</label>
                      <input type="date" id="fechaInicio" class="form-control" th:value="${fechaInicio}">
                    </div>
                  </div>
                  <div class="col-md-3">
                    <div class="form-group">
                      <label class="form-label">Fecha Fin</label>
                      <input type="date" id="fechaFin" class="form-control" th:value="${fechaFin}">
                    </div>
                  </div>
                </div>

                <!-- Selección de Espacios Deportivos -->
                <div class="form-group">
                  <label class="form-label">Espacios Deportivos</label>
                  <div id="espaciosContainer" class="row">
                    <div th:each="espacio : ${espacios}" class="col-md-6">
                      <div class="espacio-card" th:data-espacio-id="${espacio.id}">
                        <div class="d-flex align-items-center">
                          <input type="checkbox" class="form-check-input me-3" th:id="'espacio_' + ${espacio.id}">
                          <div>
                            <h6 class="mb-1" th:text="${espacio.nombre}"></h6>
                            <small class="text-muted" th:text="${espacio.tipoEspacio.nombre}"></small>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                <!-- Formulario de Horarios -->
                <div id="horarioForm" class="horario-form" style="display: none;">
                  <h5 class="mb-3">Asignar Horario</h5>
                  <p class="text-muted mb-3">El horario se aplicará durante todo el rango de fechas seleccionado.</p>

                  <!-- Horarios -->
                  <div class="row">
                    <div class="col-md-6">
                      <div class="form-group">
                        <label class="form-label">Hora de Entrada</label>
                        <input type="time" id="horaEntrada" class="form-control">
                      </div>
                    </div>
                    <div class="col-md-6">
                      <div class="form-group">
                        <label class="form-label">Hora de Salida</label>
                        <input type="time" id="horaSalida" class="form-control">
                      </div>
                    </div>
                  </div>

                  <!-- Espacio Seleccionado -->
                  <div class="form-group">
                    <label class="form-label">Espacio Seleccionado</label>
                    <select id="espacioSeleccionado" class="form-select">
                      <option value="">Seleccione un espacio</option>
                    </select>
                  </div>

                  <button type="button" id="btnAsignarHorario" class="btn btn-primary">
                    <i class="fa fa-save me-2"></i>Asignar Horario
                  </button>
                </div>
              </div>
            </div>
          </div>

          <!-- Vista Previa de Horarios -->
          <div class="col-lg-4">
            <div class="box">
              <div class="box-header with-border">
                <h4 class="box-title">
                  <i class="fa fa-list me-2"></i>Horarios Asignados
                </h4>
              </div>
              <div class="box-body">
                <div id="horariosAsignados">
                  <p class="text-muted text-center">Seleccione un coordinador para ver sus horarios</p>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Calendario -->
        <div class="row mt-4">
          <div class="col-12">
            <div class="box">
              <div class="box-header with-border">
                <h4 class="box-title">
                  <i class="fa fa-calendar me-2"></i>Vista de Calendario
                </h4>
                <div class="box-tools">
                  <button type="button" id="btnActualizarCalendario" class="btn btn-sm btn-primary">
                    <i class="fa fa-refresh me-1"></i>Actualizar
                  </button>
                </div>
              </div>
              <div class="box-body">
                <div class="calendario-container">
                  <div id="calendario"></div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>
    </div>
  </div>
</div>

<!-- FullCalendar JS -->
<script src='https://cdn.jsdelivr.net/npm/fullcalendar@6.1.8/main.min.js'></script>
<script src='https://cdn.jsdelivr.net/npm/fullcalendar@6.1.8/locales/es.js'></script>

<!-- Vendors JS -->
<script th:src="@{/js/vendors.min.js}"></script>
<script th:src="@{/js/pages/chat-popup.js}"></script>
<script th:src="@{/assets/icons/feather-icons/feather.min.js}"></script>

<!-- Template JS -->
<script th:src="@{/js/template.js}"></script>

<script>
// Variables globales
let calendario;
let espaciosSeleccionados = [];

document.addEventListener('DOMContentLoaded', function() {
    inicializarCalendario();
    configurarEventos();
});

function inicializarCalendario() {
    const calendarEl = document.getElementById('calendario');
    
    calendario = new FullCalendar.Calendar(calendarEl, {
        initialView: 'timeGridWeek',
        locale: 'es',
        headerToolbar: {
            left: 'prev,next today',
            center: 'title',
            right: 'dayGridMonth,timeGridWeek,timeGridDay'
        },
        slotMinTime: '06:00:00',
        slotMaxTime: '22:00:00',
        height: 'auto',
        events: function(info, successCallback, failureCallback) {
            cargarEventosCalendario(info.start, info.end, successCallback, failureCallback);
        },
        eventClick: function(info) {
            mostrarDetallesEvento(info.event);
        }
    });
    
    calendario.render();
}

function configurarEventos() {
    // Selección de espacios
    document.querySelectorAll('.espacio-card').forEach(card => {
        card.addEventListener('click', function() {
            const checkbox = this.querySelector('input[type="checkbox"]');
            checkbox.checked = !checkbox.checked;
            
            if (checkbox.checked) {
                this.classList.add('selected');
                espaciosSeleccionados.push({
                    id: this.dataset.espacioId,
                    nombre: this.querySelector('h6').textContent
                });
            } else {
                this.classList.remove('selected');
                espaciosSeleccionados = espaciosSeleccionados.filter(e => e.id !== this.dataset.espacioId);
            }
            
            actualizarFormularioHorarios();
        });
    });

    // Eventos removidos para días de la semana

    // Asignar horario
    document.getElementById('btnAsignarHorario').addEventListener('click', asignarHorario);

    // Actualizar calendario
    document.getElementById('btnActualizarCalendario').addEventListener('click', function() {
        calendario.refetchEvents();
    });

    // Cambio de coordinador
    document.getElementById('coordinadorSelect').addEventListener('change', function() {
        cargarHorariosCoordinador();
        calendario.refetchEvents();
    });
}

function actualizarFormularioHorarios() {
    const form = document.getElementById('horarioForm');
    const select = document.getElementById('espacioSeleccionado');
    
    if (espaciosSeleccionados.length > 0) {
        form.style.display = 'block';
        
        // Actualizar select de espacios
        select.innerHTML = '<option value="">Seleccione un espacio</option>';
        espaciosSeleccionados.forEach(espacio => {
            const option = document.createElement('option');
            option.value = espacio.id;
            option.textContent = espacio.nombre;
            select.appendChild(option);
        });
    } else {
        form.style.display = 'none';
    }
}

function asignarHorario() {
    const coordinadorId = document.getElementById('coordinadorSelect').value;
    const espacioId = document.getElementById('espacioSeleccionado').value;
    const horaEntrada = document.getElementById('horaEntrada').value;
    const horaSalida = document.getElementById('horaSalida').value;
    const fechaInicio = document.getElementById('fechaInicio').value;
    const fechaFin = document.getElementById('fechaFin').value;

    if (!coordinadorId || !espacioId || !horaEntrada || !horaSalida || !fechaInicio || !fechaFin) {
        mostrarAlerta('Por favor complete todos los campos', 'warning');
        return;
    }

    // Validar que la hora de entrada sea menor que la de salida
    if (horaEntrada >= horaSalida) {
        mostrarAlerta('La hora de entrada debe ser anterior a la hora de salida', 'warning');
        return;
    }

    // Validar que la fecha de inicio sea menor o igual a la de fin
    if (new Date(fechaInicio) > new Date(fechaFin)) {
        mostrarAlerta('La fecha de inicio debe ser anterior o igual a la fecha de fin', 'warning');
        return;
    }

    const datos = {
        coordinadorId: parseInt(coordinadorId),
        espacioId: parseInt(espacioId),
        horaEntrada: horaEntrada,
        horaSalida: horaSalida,
        fechaInicio: fechaInicio,
        fechaFin: fechaFin
    };

    fetch('/admin/asignar-horario', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(datos)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            mostrarAlerta(data.message, 'success');
            limpiarFormulario();
            calendario.refetchEvents();
            cargarHorariosCoordinador();
        } else {
            mostrarAlerta(data.message, 'error');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        mostrarAlerta('Error al asignar horario', 'error');
    });
}

function cargarEventosCalendario(start, end, successCallback, failureCallback) {
    const coordinadorId = document.getElementById('coordinadorSelect').value;
    const params = new URLSearchParams({
        fechaInicio: start.toISOString().split('T')[0],
        fechaFin: end.toISOString().split('T')[0]
    });
    
    if (coordinadorId) {
        params.append('coordinadorId', coordinadorId);
    }

    fetch(`/admin/calendario-horarios?${params}`)
        .then(response => response.json())
        .then(data => {
            const eventos = data.map(horario => ({
                id: horario.id,
                title: horario.title,
                start: horario.start + 'T' + horario.startTime,
                end: horario.start + 'T' + horario.endTime,
                color: horario.color,
                extendedProps: {
                    espacioNombre: horario.espacioNombre,
                    coordinadorNombre: horario.coordinadorNombre,
                    tipoEspacio: horario.tipoEspacio
                }
            }));
            successCallback(eventos);
        })
        .catch(error => {
            console.error('Error cargando eventos:', error);
            failureCallback(error);
        });
}

function cargarHorariosCoordinador() {
    const coordinadorId = document.getElementById('coordinadorSelect').value;
    const container = document.getElementById('horariosAsignados');
    
    if (!coordinadorId) {
        container.innerHTML = '<p class="text-muted text-center">Seleccione un coordinador para ver sus horarios</p>';
        return;
    }
    
    // Aquí podrías cargar una lista resumida de horarios
    container.innerHTML = '<p class="text-info text-center">Cargando horarios...</p>';
}

function mostrarDetallesEvento(evento) {
    const detalles = `
        <strong>Coordinador:</strong> ${evento.extendedProps.coordinadorNombre}<br>
        <strong>Espacio:</strong> ${evento.extendedProps.espacioNombre}<br>
        <strong>Tipo:</strong> ${evento.extendedProps.tipoEspacio}<br>
        <strong>Horario:</strong> ${evento.start.toLocaleTimeString()} - ${evento.end.toLocaleTimeString()}
    `;
    
    if (confirm(`${detalles}\n\n¿Desea eliminar este horario?`)) {
        eliminarHorario(evento.id);
    }
}

function eliminarHorario(horarioId) {
    fetch(`/admin/eliminar-horario/${horarioId}`, {
        method: 'DELETE'
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            mostrarAlerta(data.message, 'success');
            calendario.refetchEvents();
            cargarHorariosCoordinador();
        } else {
            mostrarAlerta(data.message, 'error');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        mostrarAlerta('Error al eliminar horario', 'error');
    });
}

function limpiarFormulario() {
    document.getElementById('horaEntrada').value = '';
    document.getElementById('horaSalida').value = '';
    document.getElementById('espacioSeleccionado').value = '';
}

function mostrarAlerta(mensaje, tipo) {
    const alertClass = tipo === 'success' ? 'alert-success' : 
                     tipo === 'warning' ? 'alert-warning' : 'alert-danger';
    
    const alerta = document.createElement('div');
    alerta.className = `alert ${alertClass} alert-custom alert-dismissible fade show`;
    alerta.innerHTML = `
        ${mensaje}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    
    document.querySelector('.content').insertBefore(alerta, document.querySelector('.content').firstChild);
    
    setTimeout(() => {
        alerta.remove();
    }, 5000);
}
</script>

</body>
</html>
