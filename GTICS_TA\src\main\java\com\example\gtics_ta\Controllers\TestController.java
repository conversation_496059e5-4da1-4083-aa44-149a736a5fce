package com.example.gtics_ta.Controllers;

import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import jakarta.servlet.http.HttpSession;

@Controller
@RequestMapping("/test")
public class TestController {

    @GetMapping("/session")
    @ResponseBody
    public String testSession(HttpSession session) {
        Object usuario = session.getAttribute("usuario");
        return "Usuario en sesión: " + (usuario != null ? usuario.toString() : "null") + 
               "<br>Session ID: " + session.getId() +
               "<br>Session válida: " + !session.isNew();
    }

    @GetMapping("/csrf")
    @ResponseBody
    public String testCsrf() {
        return "CSRF test endpoint - si ves esto, el controlador funciona";
    }
}
